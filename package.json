{"name": "personal-budget-allocation-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"firebase": "^11.8.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.2.1", "typescript": "~5.7.2", "vite": "^6.2.0"}}